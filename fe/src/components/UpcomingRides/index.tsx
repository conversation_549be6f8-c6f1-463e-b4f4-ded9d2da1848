/* eslint-disable react-hooks/exhaustive-deps */
/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect, useCallback } from "react";
import { useNavigate } from "react-router-dom";
import { Calendar, Clock, Users, Lock, Briefcase } from "lucide-react";
import { SearchFilters } from "../SearchSection";
import { SignInModal } from "../SignInModal";
import { RideDetailsModal } from "../RideDetailsModal";
import { Ride, RideRequest } from "../../types";
import { getAllRides, getAllRideRequests, getUserBookings, getUserRides, getRideBookings, updateBookingStatus } from "../../lib/api";
import { getStoredUser } from "../../lib/auth";
import { Loader } from "../Loader";
import { ActiveRideRequests } from "../ActiveRideRequests";
import { getSchoolFromEmail } from "../../lib/schoolUtils";
import styles from "./UpcomingRides.module.css";
import { getUserEmail } from "../../lib/api";
import apiClient from "../../lib/api/client";
import { convertUTCToLocal } from "../../utils/convertUTCToLocal";

const formatDisplayName = (fullName: string): string => {
  return fullName.split(' ')[0]; // Get only the first name
};

type UpcomingRidesProps = {
  filters: SearchFilters;
  isSignedIn: boolean;
  region: string;
  onDateCounts?: (dateCounts: Record<string, number>) => void;
};



export function UpcomingRides({ filters, isSignedIn, region }: UpcomingRidesProps) {
  const navigate = useNavigate();
  const [isSignInModalOpen, setIsSignInModalOpen] = useState(false);
  const [selectedRide, setSelectedRide] = useState<Ride | null>(null);
  const [rides, setRides] = useState<Ride[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [userBookings, setUserBookings] = useState<any[]>([]);
  const User = getStoredUser();
  const [activeTab, setActiveTab] = useState("all-rides");
  const [myRides, setMyRides] = useState<Ride[]>([]);
  const [rideRequests, setRideRequests] = useState<RideRequest[]>([]);
  const [driverEmail, setDriverEmail] = useState<Record<string, string>>({});


  useEffect(() => {
    const fetchBookings = async () => {
      if (!User?.token) return;
      try {
        const bookings = await getUserBookings();
        setUserBookings(Array.isArray(bookings) ? bookings : []);
      } catch (err) {
        console.error("Error fetching bookings:", err);
      }
    };

    if (isSignedIn) {
      fetchBookings();
    }
  }, [isSignedIn, User?.token]);

  const formatDate = (dateString: string) => {
    return new Date(dateString + "T00:00:00").toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "2-digit",
      timeZone: "UTC",
    });
  };

  const fetchRides = async () => {
    try {
      setIsLoading(true);
      setError(null);

      const getDateRange = (dateStr: string) => {
        const date = new Date(dateStr);
        const start = new Date(date);
        const end = new Date(date);
        start.setDate(date.getDate()); // Can do +/- N days to set a range
        end.setDate(date.getDate());  // Can do +/- N days 
        // Format as yyyy-mm-dd
        const toISO = (d: Date) => d.toISOString().slice(0, 10);
        return { startDate: toISO(start), endDate: toISO(end) };
      };

      let dateRange = {};
      if (filters.date) {
        dateRange = getDateRange(filters.date);
      }

      const apiRides = await getAllRides({
        from: filters.from || undefined,
        to: filters.to || undefined,
        colleges: filters.colleges.length > 0 ? filters.colleges : undefined,
        ...dateRange,
      });

      // Ensure rides is always an array
      setRides(Array.isArray(apiRides) ? apiRides : []);
      console.log({ rides: apiRides, userBookings });
    } catch (err) {
      setError("Failed to fetch rides. Please try again later.");
      console.error("Error fetching rides:", err);
      setRides([]);
    } finally {
      setIsLoading(false);
    }
  };

  // Refresh rides and my rides when a new ride is posted or a booking is made
  useEffect(() => {
    window.refreshRides = fetchRides;
    window.refreshMyRides = fetchMyRides;
    window.refreshRideRequests = fetchRideRequests;
  }, []);

  useEffect(() => {
    fetchRides();
    fetchMyRides();    fetchRideRequests(); // Get ride requests on initial load and filter changes

  }, [filters.from, filters.to, filters.date, filters.colleges]);


  const formatLocation = (location: string): string => {
    return location
      .split("_")
      .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
      .join(" ");
  };
  const handleReserveClick = (ride: Ride) => {
    if (!isSignedIn) {
      setIsSignInModalOpen(true);
    } else {
      setSelectedRide(ride);
    }
  };

  const hasUserBookedRide = (rideId: string) => {
    return userBookings.some((booking) => booking.rideId === rideId);
  };

  const handleBookingSuccess = (
    rideId: string,
    seatsBooked: number,
    suitcasesBooked: number
  ) => {
    // Update rides list
    setRides((prevRides) =>
      prevRides.map((r) => {
        if (r.id === rideId) {
          return {
            ...r,
            seatsAvailable: r.seatsAvailable - seatsBooked,
            suitcasesAvailable: r.suitcasesAvailable - suitcasesBooked,
          };
        }
        return r;
      })
    );

    // Update selected ride
    setSelectedRide((prev) => {
      if (prev && prev.id === rideId) {
        return {
          ...prev,
          seatsAvailable: prev.seatsAvailable - seatsBooked,
          suitcasesAvailable: prev.suitcasesAvailable - suitcasesBooked,
        };
      }
      return prev;
    });
  };

  // Fetch all ride requests
  const fetchRideRequests = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const apiRideRequests = await getAllRideRequests({
        from: filters.from || undefined,
        to: filters.to || undefined,
        date: filters.date || undefined,
      });
      setRideRequests(apiRideRequests);
    } catch (err) {
      setError("Failed to fetch ride requests. Please try again later.");
      console.error("Error fetching ride requests:", err);
    } finally {
      setIsLoading(false);
    }
  };

  // Fetch my rides and their passengers
  const fetchMyRides = async () => {
    if (!User?.token) return;
    setIsLoading(true);
    setError(null);
    try {
      const rides = await getUserRides();
      // For each ride, fetch passengers
      const ridesWithPassengers = await Promise.all(
        rides.map(async (ride: any) => {
          const passengers = await getRideBookings(ride.id);
          return { ...ride, passengers };
        })
      );
      setMyRides(ridesWithPassengers);
    } catch (err) {
      setError("Failed to fetch your rides. Please try again later.");
      setMyRides([]);
    } finally {
      setIsLoading(false);
    }
  };


  // If the user signs out on the My Rides page, redirect back to All Rides.
  useEffect(() => {
    if (!isSignedIn && activeTab != "all-rides") {
      setActiveTab("all-rides");
    }
  }, [isSignedIn]);

  const handleAcceptPassenger = async (bookingId: string) => {
    const confirmed = window.confirm("Are you sure you want to accept this passenger? This action cannot be undone.");
    if (!confirmed) return;
    try {
      await updateBookingStatus(bookingId, "confirmed");
      fetchMyRides();
    } catch (error) {
      console.log("Failed to update booking status to confirmed", error);
    }
  };

  const handleRejectPassenger = async (bookingId: string) => {
    const confirmed = window.confirm("Are you sure you want to reject this passenger? This action cannot be undone.");
    if (!confirmed) return;
    try {
      await updateBookingStatus(bookingId, "cancelled");
      fetchMyRides();
    } catch (error) {
      console.error("Failed to update booking status to cancelled:", error);
    }
  };

  const handleDeleteRide = async (rideId: string) => {
    const confirmed = window.confirm('Are you sure you want to cancel this ride? This action cannot be undone.');
    if (!confirmed) return;

    try {
      const response = await fetch(`${import.meta.env.VITE_API_URL}/api/rides/${rideId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${User?.token}`,
          'Content-Type': 'application/json',
        },
      });

      if (response.ok) {
        // Refresh the rides list
        fetchMyRides();
        alert('Ride canceled successfully. All passengers have been notified and refunds have been processed for confirmed bookings.');
      } else {
        const error = await response.json();
        alert(`Failed to cancel ride: ${error.message || 'Unknown error'}`);
      }
    } catch (error) {
      console.error('Error cancelling ride:', error);
      alert('Failed to cancel ride. Please try again.');
    }
  };

    // Ensure rides is an array before rendering
    const ridesToRender = Array.isArray(rides) ? rides : [];

    const getDriverEmails = useCallback(async (): Promise<void> => {
      console.log("getDriverEmails called with ridesToRender:", ridesToRender.length);
      const emails: {[key: string]: string} = {};
      const promises = ridesToRender
        .map(async (ride) => {
          // Only fetch if we don't already have this driver's email
          if (!driverEmail[ride.driver.id]) {
            try {
              console.log("Fetching email for: ", ride.driver.id);
              const email = await getUserEmail(ride.driver.id);
              console.log("Got email:", email);
              if (email) {
                emails[ride.driver.id] = email;
              }
            } catch (error) {
              console.error(`Error getting email for driver ${ride.driver.id}:`, error);
            }
          } else {
            console.log("Email already cached for driver:", ride.driver.id);
          }
        });
      await Promise.all(promises);
      if (Object.keys(emails).length > 0) {
        console.log("Setting new emails:", emails);
        setDriverEmail(prev => ({ ...prev, ...emails }));
      }
    }, [ridesToRender]);

    // Get driver email for school check
    useEffect(() => {
      console.log("useEffect triggered - ridesToRender:", ridesToRender.length, "User token:", !!User?.token);
      if (!User?.token) return;
      if (ridesToRender.length > 0) {
        getDriverEmails();
      }
    }, [ridesToRender, User?.token, getDriverEmails]);



    const getDriverSchool = (driverId: string): string | null => {
      const email = driverEmail[driverId]
      console.log("getDriverSchool called for driverId:", driverId, "email:", email);
      if (!email) return null;
      const school = getSchoolFromEmail(email);
      console.log("School detected:", school);
      return school;
    }

    function renderSchoolBadge(school:string | null): JSX.Element | null {
      if (!school) return null;
      if (school === "cornell") {
        return <span className={styles.cornellBadge}>Cornell</span>;
      } else if (school === "ithaca") {
        return <span className={styles.ithacaBadge}>Ithaca</span>;
      } else if (school === "syracuse") {
        return <span className={styles.syracuseBadge}>Syracuse</span>;
      } else if (school === "binghamton") {
        return <span className={styles.binghamtonBadge}>Binghamton</span>;
      } else {
        return null;
      }
    }

  if (isLoading) {
    return (
      <div className={styles.loadingContainer}>
        <Loader size="lg" className="mb-4" />
        <p className={styles.loadingText}>Loading rides...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className={styles.errorContainer}>
        <p className={styles.errorText}>{error}</p>
      </div>
    );
  }

  const getUserBookingStatus = (rideId: string): string | null => {
    const booking = userBookings.find((b) => b.rideId === rideId);
    return booking ? booking.status : null;
  };

  return (
    <div className={styles.container}>
      <div className={styles.header}>
        <h2 className={styles.title}>
          {activeTab === "all-ride-requests" ? "Upcoming ride requests" : "Upcoming rides"}
        </h2>
        <p className={styles.subtitle}>
          Showing {activeTab === "rides-i-posted" ? myRides.length : (activeTab === "all-ride-requests" ? rideRequests.length : ridesToRender.length)} available {activeTab === "rides-i-posted" ? (myRides.length === 1 ? "ride" : "rides") : (activeTab === "all-ride-requests" ? (rideRequests.length === 1 ? "ride request" : "ride requests") : (ridesToRender.length === 1 ? "ride" : "rides"))}
        </p>
        <div className="flex border-b bg-white dark:bg-gray-800 mb-6 mt-4">
          <button
            className={`px-4 py-2 font-medium ${activeTab === "all-rides"
              ? 'text-desert-primary border-b-2 border-desert-primary'
              : 'text-gray-500'}`}
            onClick={() => setActiveTab("all-rides")}
            style={{
              marginRight: 8,
              padding: '6px 16px',
              cursor: 'pointer',
            }}
          >
            All Rides
          </button>
          <button
            className={`px-4 py-2 font-medium ${activeTab === "rides-i-posted"
              ? 'text-desert-primary border-b-2 border-desert-primary'
              : 'text-gray-500'}`}
            onClick={() => setActiveTab("rides-i-posted")}
            style={{
              padding: '6px 16px',
              cursor: 'pointer',
            }}
            disabled={!isSignedIn}
            title={isSignedIn ? '' : 'Sign in to see your rides'}
          >
            Rides I Posted
          </button>

          <button
            className={`px-4 py-2 font-medium ${activeTab === "all-ride-requests"
              ? 'text-desert-primary border-b-2 border-desert-primary'
              : 'text-gray-500'}`}
            onClick={() => setActiveTab("all-ride-requests")}
            style={{
              padding: '6px 16px',
              cursor: 'pointer',
            }}
            disabled={!isSignedIn}
            title={isSignedIn ? '' : 'Sign in to see ride requests'}
          >
            All Ride Requests
          </button>

        </div>
      </div>

      {/* My Rides View */}
      {activeTab === "rides-i-posted" ? (
        isLoading ? (
          <div className={styles.loadingContainer}>
            <Loader size="lg" className="mb-4" />
            <p className={styles.loadingText}>Loading your rides...</p>
          </div>
        ) : error ? (
          <div className={styles.errorContainer}>
            <p className={styles.errorText}>{error}</p>
          </div>
        ) : myRides.length === 0 ? (
          <div className={styles.errorContainer}>
            <p className={styles.errorText}>You haven't posted any rides yet.</p>
          </div>
        ) : (
          <div className={styles.ridesList}>
            {myRides.map((ride) => (
              <div key={ride.id} className={styles.rideCard}>
                <div className={styles.rideHeader}>
                  <div>
                    <div className={styles.rideRoute}>
                      {formatLocation(ride.from)} → {formatLocation(ride.to)}
                    </div>
                    {ride.pickupAddress && (
                      <div>
                        <span className={styles.rideRoute}>Pick Up: </span>
                        <span className={styles.rideRoute}>{ride.pickupAddress}</span>
                      </div>
                    )}
                    {ride.dropoffAddress && (
                      <div>
                        <span className={styles.rideRoute}>Drop Off: </span>
                        <span className={styles.rideRoute}>{ride.dropoffAddress}</span>
                      </div>
                    )}
                    <div className={styles.postedTime}>
                      Posted {ride.postedTime}
                    </div>
                  </div>
                  <div className={styles.priceContainer}>
                    <div className={styles.price}>
                      ${ride.price}
                      <span className={styles.pricePerSeat}>/seat</span>
                    </div>
                  </div>
                </div>
                <div className={styles.detailsContainer}>
                  <div className={styles.detailItem}>
                    <Calendar className={styles.detailIcon} />
                    <span>
                      {convertUTCToLocal(ride.date, ride.time).localDate}
                    </span>
                  </div>
                  <div className={styles.detailItem}>
                    <Clock className={styles.detailIcon} />
                    <span>Departing at {convertUTCToLocal(ride.date, ride.time).localTime}</span>
                  </div>
                  <div className={styles.detailItem}>
                    <Users className={styles.detailIcon} />
                    <span>
                      {ride.seatsAvailable} of {ride.totalSeats} seats available
                    </span>
                  </div>
                  <div className={styles.detailItem}>
                    <Briefcase className={styles.detailIcon} />
                    <span>
                      {ride.suitcasesAvailable} of {ride.totalSuitcases} suitcases allowed
                    </span>
                  </div>
                </div>
                {ride.notes && ride.notes.length > 0 && (
                  <div className={styles.notesSection}>
                    <div className={styles.notesList}>
                      {ride.notes.map((note: string, index: number) => (
                        <div key={index} className={styles.noteItem}>
                          {note}
                        </div>
                      ))}
                    </div>
                  </div>
                )}
                {/* Passengers Section */}
                <div style={{ marginTop: 16 }}>
                  <div style={{ fontWeight: 600, marginBottom: 4 }}>Passengers:</div>
                  {ride.passengers && ride.passengers.length > 0 ? (
                    <div style={{ display: 'flex', flexDirection: 'column', gap: 8 }}>
                      {ride.passengers.map((passenger: any) => (

                        <div key={passenger.id} style={{ display: 'flex', alignItems: 'center', gap: 12, marginBottom: 4 }}>
                          <span>
                            <div
                              className={`${styles.passengerAvatar}`}
                              onClick={() => navigate(`/${region}/profile/${passenger.user.id}`)}
                              style={{ cursor: 'pointer' }}
                            >
                              {
                                passenger.user?.avatar ? (
                                  <img
                                    src={passenger.user.avatar}
                                    alt={passenger.user.name}
                                    className="w-full h-full rounded-full object-cover"

                                  />
                                ) : (
                                  passenger.user?.name[0]
                                )}
                            </div>
                          </span>
                          <span style={{ minWidth: 120 }}>
                            <div
                              className={styles.passengerName}
                              onClick={() => navigate(`/${region}/profile/${passenger.user.id}`)}
                              style={{ cursor: 'pointer' }}>
                              {passenger.user?.name || 'Unknown'}
                            </div>
                            <div className={styles.passengerInfo}>
                              ({passenger.seats} seat{passenger.seats !== 1 ? 's' : ''}, {passenger.suitcases} suitcase{passenger.suitcases !== 1 ? 's' : ''})
                            </div>
                          </span>
                          <div className="flex gap-2">
                            {passenger.status === 'cancelled' ? (
                              <span className="text-xs font-semibold px-3 py-1 rounded bg-red-600 text-white"
                                style={{
                                  backgroundColor: '#ffa3a3',
                                  color: 'white',
                                  borderRadius: '1.0rem'
                                }}
                              >
                                REJECTED
                              </span>
                            ) : passenger.status === 'confirmed' ? (
                              <span
                                className="text-xs font-semibold px-3 py-1 rounded"
                                style={{
                                  backgroundColor: '#7ed9a7',
                                  color: 'white',
                                  borderRadius: '1.0rem'
                                }}
                              >
                                ACCEPTED
                              </span>
                            ) : passenger.status === 'completed' ? (
                              <span
                                className="text-xs font-semibold px-3 py-1 rounded"
                                style={{
                                  backgroundColor: '#4f8cff', // gray
                                  color: 'white',
                                  borderRadius: '1.0rem'
                                }}
                              >
                                COMPLETED
                              </span>
                            ) : (
                              <div className="flex gap-2">
                                <button
                                  onClick={() => handleAcceptPassenger(passenger.id)}
                                  className="text-green-600 text-xs font-semibold px-2 py-1 border border-green-600 rounded hover:bg-green-600 hover:text-white transition"
                                >
                                  Accept
                                </button>
                                <button
                                  onClick={() => handleRejectPassenger(passenger.id)}
                                  className="text-red-600 text-xs font-semibold px-2 py-1 border border-red-600 rounded hover:bg-red-600 hover:text-white transition"
                                >
                                  Reject
                                </button>
                              </div>
                            )}
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <span style={{ color: '#888' }}>No passengers yet</span>
                  )}
                </div>
                {ride.canDelete && (
                  <button
                    onClick={() => handleDeleteRide(ride.id)}
                    className={styles.cancelRideButton}
                  >
                    Cancel Ride
                  </button>
                )}
              </div>
            ))}
          </div>
        )
      ) : activeTab === "all-ride-requests" ? (
        // All Ride Requests View
        <ActiveRideRequests filters={filters} isSignedIn={!!User} region={region} rideRequests={rideRequests} error={error} isLoading={isLoading} />
      ) : (
        // Default All Rides View
        <div className={styles.ridesList}>
          {ridesToRender.map((ride) => {
            console.log(ride.driver.id, User?.id);
            return (
            <div
              key={ride.id}
              className={styles.rideCard}
            >
              <div className={styles.rideHeader}>
                <div>
                  <div className={styles.rideRoute}>
                    {formatLocation(ride.from)} → {formatLocation(ride.to)}
                  </div>
                  {ride.pickupAddress && (
                      <div className={styles.pickupAddress}>
                        <span>Pick Up: </span>
                        <span>{ride.pickupAddress}</span>
                      </div>
                    )}
                    {ride.dropoffAddress && (
                      <div className={styles.dropoffAddress}>
                        <span>Drop Off: </span>
                        <span>{ride.dropoffAddress}</span>
                      </div>
                    )}
                  <div className={styles.postedTime}>
                    Posted {ride.postedTime}
                  </div>
                </div>
                <div className={styles.priceContainer}>
                  <div className={styles.price}>
                    ${ride.price}
                    <span className={styles.pricePerSeat}>/seat</span>
                  </div>
                  <button
                    className={`
                      ${styles.reserveButton}
                      ${(() => {
                        const status = getUserBookingStatus(ride.id);
                        if (status === "confirmed") return styles.acceptedButton;
                        if (status === "cancelled") return styles.rejectedButton;
                        if (status === "pending") return styles.pendingButton;
                        return "";
                      })()}
                    `}
                    onClick={
                      (() => {
                        const status = getUserBookingStatus(ride.id);
                        if (status === "cancelled") return undefined;
                        return () => handleReserveClick(ride);
                      })()
                    }
                    disabled={getUserBookingStatus(ride.id) === "cancelled"}
                  >
                    {ride.driver.id === User?.id
                      ? "Details"
                      : (() => {
                          const status = getUserBookingStatus(ride.id);
                          if (!status) return "Reserve";
                          if (status === "pending") return "Pending";
                          if (status === "confirmed") return "Confirmed";
                          if (status === "cancelled") return "Declined";
                          return "Details";
                        })()
                    }
                  </button>
                </div>
              </div>

              <div className={styles.detailsContainer}>
                <div className={styles.detailItem}>
                  <Calendar className={styles.detailIcon} />
                  <span>
                    {convertUTCToLocal(ride.date, ride.time).localDate}
                  </span>
                </div>
                <div className={styles.detailItem}>
                  <Clock className={styles.detailIcon} />
                  <span>Departing at {convertUTCToLocal(ride.date, ride.time).localTime}</span>
                </div>
                <div className={styles.detailItem}>
                  <Users className={styles.detailIcon} />
                  <span>
                    {ride.seatsAvailable} of {ride.totalSeats} seats available
                  </span>
                </div>
                <div className={styles.detailItem}>
                  <Briefcase className={styles.detailIcon} />
                  <span>
                    {ride.suitcasesAvailable} of {ride.totalSuitcases} suitcases
                    allowed
                  </span>
                </div>
              </div>

              {ride.notes && ride.notes.length > 0 && (
                <div className={styles.notesSection}>
                  <div className={styles.notesList}>
                    {ride.notes.map((note, index) => (
                      <div key={index} className={styles.noteItem}>
                        {note}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              <div className={styles.driverContainer}>
                <div
                  className={`${styles.driverAvatar} ${ride.driver.avatarColor}`}
                  onClick={() => navigate(`/${region}/profile/${ride.driver.id}`)}
                  style={{ cursor: 'pointer' }}
                >
                  {isSignedIn ? (
                    ride.driver.avatar ? (
                      <img
                        src={ride.driver.avatar}
                        alt={ride.driver.name}
                        className="w-full h-full rounded-full object-cover"

                      />
                    ) : (
                      ride.driver.name[0]
                    )
                  ) : (
                    <Lock className="w-4 h-4" />
                  )}
                </div>
                <div className={styles.driverInfo}>
                  {isSignedIn ? (
                    <div style={{ display: 'flex', alignItems: 'center', gap: '0.4em' }}>
                      <div
                        className={styles.driverName}
                        onClick={() => navigate(`/${region}/profile/${ride.driver.id}`)}
                        style={{ cursor: 'pointer' }}
                      >
                        {formatDisplayName(ride.driver.name)}
                      </div>
                      {renderSchoolBadge(getDriverSchool(ride.driver.id))}
                    </div>
                  ) : (
                    <div className={styles.signInPrompt}>
                      <button
                        className={styles.signInLink}
                        onClick={() => setIsSignInModalOpen(true)}
                      >
                        Sign in
                      </button>{" "}
                      to view Cornell Verified driver details
                    </div>
                  )}
                </div>
              </div>
            </div>
          );
          })}
        </div>
      )}

      <SignInModal
        isOpen={isSignInModalOpen}
        onClose={() => setIsSignInModalOpen(false)}
        onSignInClick={() => {
          // This should trigger the main auth modal to open
          window.dispatchEvent(new CustomEvent('open-auth-modal'));
        }}
      />

      {selectedRide && (
        <RideDetailsModal
          isOpen={!!selectedRide}
          onClose={() => setSelectedRide(null)}
          ride={selectedRide}
          userId={User?.id}
          userName={User?.name}
          token={User?.token || null}
          bookings={userBookings}
          setRides={setRides}
          setUserBookings={setUserBookings}
          onBookingSuccess={handleBookingSuccess}
          region={region}
          // apiUrl={"https://api-web-app.kamelride.com"}
        />
      )}
    </div>
  );
}