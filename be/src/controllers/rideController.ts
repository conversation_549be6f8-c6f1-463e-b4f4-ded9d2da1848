import { Request, Response } from "express";
import { Op } from "sequelize";
import { Ride } from "../models/rideModel.js";
import { User } from "../models/userModel.js";
import { asyncHandler, AppError } from "../middleware/errorMiddleware.js";
import { getSchoolFromEmail } from "../utils/schoolUtils.js";

import { Booking, BookingStatus } from '../models/bookingModel.js';
import { sendDynamicEmail } from '../utils/sendGrid.js';
import { toZonedTime, format } from 'date-fns-tz';

import Stripe from 'stripe';

const stripe = new Stripe(process.env.STRIPE_SECRET_KEY!, { apiVersion: "2025-05-28.basil" });
const STRIPE_ENABLED = process.env.STRIPE_ENABLED === 'true';

// @desc    Create a new ride
// @route   POST /api/rides
// @access  Private
export const createRide = asyncHandler(async (req: Request, res: Response) => {
  const { from, to, date, time, totalSeats, totalSuitcases, price, notes,
          pickupAddress, pickupLat, pickupLng,
          dropoffAddress, dropoffLat, dropoffLng
        } =
          req.body;

  const user = req.user;

  if (!user) {
    throw new AppError("User not found", 404);
  }

  // Create new ride
  const ride = await Ride.create({
    from,
    to,
    date,
    time,
    seatsAvailable: totalSeats,
    totalSeats,
    suitcasesAvailable: totalSuitcases,
    totalSuitcases,
    price,
    notes: notes || [],
    driverId: user.id,
    pickupAddress,
    pickupLat,
    pickupLng,
    dropoffAddress,
    dropoffLat,
    dropoffLng,
  });

  res.status(201).json(ride);
});

// @desc    Get all rides with filters
// @route   GET /api/rides
// @access  Public
export const getRides = asyncHandler(async (req: Request, res: Response) => {

  const { from, to, date, minSeats, minSuitcases, maxPrice, startDate, endDate, colleges } = req.query;

  // Build filter object
  const filter: any = {};

  if (from) {
    filter.from = from;
  }

  if (to) {
    filter.to = to;
  }

  // Date range filter

  if (startDate && endDate) {
    filter.date = {
      [Op.gte]: startDate,
      [Op.lte]: endDate,
    };
  } else if (date) {
    filter.date = date;
  }

  if (minSeats) {
    filter.seatsAvailable = { [Op.gte]: parseInt(minSeats as string) };
  }

  if (minSuitcases) {
    filter.suitcasesAvailable = { [Op.gte]: parseInt(minSuitcases as string) };
  }

  if (maxPrice) {
    filter.price = { [Op.lte]: parseFloat(maxPrice as string) };
  }

  // Get rides with filters
  let rides = await Ride.findAll({
    where: {
      ...filter,
      [Op.and]: {
        date: {
          [Op.gte]: new Date(),
        },
      },
    },
    include: [
      {
        model: User,
        as: "driver",
        attributes: ["id", "name", "avatar", "email"],
      },
    ],
    order: [
      ["date", "ASC"],
      ["time", "ASC"],
    ],
  });

  // Filter by colleges if specified
  if (colleges) {
    console.log("Filtering by colleges:", colleges);
    // Convert to array if it's a single string
    const collegesArray = Array.isArray(colleges) ? colleges : [colleges];

    if (collegesArray.length > 0) {
      rides = rides.filter((ride) => {
        const driver = ride.get("driver") as User;
        if (!driver || !driver.email) {
          console.log("Driver or email missing for ride:", ride.id);
          return false;
        }

        const driverSchool = getSchoolFromEmail(driver.email);
        console.log(`Driver ${driver.id} (${driver.email}) -> school: ${driverSchool}`);
        const shouldInclude = driverSchool && collegesArray.includes(driverSchool);
        console.log(`Should include: ${shouldInclude}`);
        return shouldInclude;
      });
      console.log("Filtered rides count:", rides.length);
    }
  }

  // Format rides for frontend
  const formattedRides = rides.map((ride) => {
    const driver = ride.get("driver") as User;

    return {
      id: ride.id,
      from: ride.from,
      to: ride.to,
      date: ride.date,
      time: ride.time,
      seatsAvailable: ride.seatsAvailable,
      totalSeats: ride.totalSeats,
      suitcasesAvailable: ride.suitcasesAvailable,
      totalSuitcases: ride.totalSuitcases,
      price: ride.price,
      postedTime: ride.postedTime,
      notes: ride.notes,
      pickupAddress: ride.pickupAddress,
      pickupLat: ride.pickupLat,
      pickupLng: ride.pickupLng,
      dropoffAddress: ride.dropoffAddress,
      dropoffLat: ride.dropoffLat,
      dropoffLng: ride.dropoffLng,
      driver: {
        id: driver.id,
        name: driver.name,
        info: req.region === 'nj' ? "NJ driver" : "Cornell Verified driver",
        avatarColor: getAvatarColor(driver.id),
        avatar: driver.avatar,
      },
    };
  });

  res.status(200).json(formattedRides);
});

// @desc    Get ride by ID
// @route   GET /api/rides/:id
// @access  Public
export const getRideById = asyncHandler(async (req: Request, res: Response) => {
  const rideId = req.params.id;

  // Find ride by ID
  const ride = await Ride.findByPk(rideId, {
    include: [
      {
        model: User,
        as: "driver",
        attributes: ["id", "name", "avatar"],
      },
    ],
  });

  if (!ride) {
    throw new AppError("Ride not found", 404);
  }

  // Format ride for frontend
  const driver = ride.get("driver") as User;

  const formattedRide = {
    id: ride.id,
    from: ride.from,
    to: ride.to,
    date: ride.date,
    time: ride.time,
    seatsAvailable: ride.seatsAvailable,
    totalSeats: ride.totalSeats,
    suitcasesAvailable: ride.suitcasesAvailable,
    totalSuitcases: ride.totalSuitcases,
    price: ride.price,
    postedTime: ride.postedTime,
    notes: ride.notes,
    pickupAddress: ride.pickupAddress,
    pickupLat: ride.pickupLat,
    pickupLng: ride.pickupLng,
    dropoffAddress: ride.dropoffAddress,
    dropoffLat: ride.dropoffLat,
    dropoffLng: ride.dropoffLng,
    driver: {
      name: driver.name,
      info: req.region === 'nj' ? "NJ driver" : "Cornell Verified driver",
      avatarColor: getAvatarColor(driver.id),
      avatar: driver.avatar,
    },
  };

  res.status(200).json(formattedRide);
});

// @desc    Update ride
// @route   PUT /api/rides/:id
// @access  Private
export const updateRide = asyncHandler(async (req: Request, res: Response) => {
  const rideId = req.params.id;
  const user = req.user;

  if (!user) {
    throw new AppError("User not found", 404);
  }

  // Find ride by ID
  const ride = await Ride.findByPk(rideId);

  if (!ride) {
    throw new AppError("Ride not found", 404);
  }

  // Check if user is the ride owner
  if (ride.driverId !== user.id && user.role !== "admin") {
    throw new AppError("Not authorized to update this ride", 403);
  }

  // Update ride data
  const { from, to, date, time, totalSeats, totalSuitcases, price, notes,
          pickupAddress, pickupLat, pickupLng,
          dropoffAddress, dropoffLat, dropoffLng
        } =
          req.body;

  if (from) ride.from = from;
  if (to) ride.to = to;
  if (date) ride.date = date;
  if (time) ride.time = time;

  if (totalSeats) {
    const seatsDiff = totalSeats - ride.totalSeats;
    ride.totalSeats = totalSeats;
    ride.seatsAvailable = Math.max(0, ride.seatsAvailable + seatsDiff);
  }

  if (totalSuitcases) {
    const suitcasesDiff = totalSuitcases - ride.totalSuitcases;
    ride.totalSuitcases = totalSuitcases;
    ride.suitcasesAvailable = Math.max(
      0,
      ride.suitcasesAvailable + suitcasesDiff
    );
  }

  if (price) ride.price = price;
  if (notes) ride.notes = notes;

  if (pickupAddress !== undefined) ride.pickupAddress = pickupAddress;
  if (pickupLat !== undefined) ride.pickupLat = pickupLat;
  if (pickupLng !== undefined) ride.pickupLng = pickupLng;
  if (dropoffAddress !== undefined) ride.dropoffAddress = dropoffAddress;
  if (dropoffLat !== undefined) ride.dropoffLat = dropoffLat;
  if (dropoffLng !== undefined) ride.dropoffLng = dropoffLng;

  // Save updated ride
  await ride.save();

  res.status(200).json(ride);
});

// @desc    Delete ride
// @route   DELETE /api/rides/:id
// @access  Private
export const deleteRide = asyncHandler(async (req: Request, res: Response) => {
  const { id } = req.params;
  const { id: userId } = req.user as { id: string };

  const ride = await Ride.findByPk(id);
  if (!ride) {
    return res.status(404).json({ error: "Ride not found" });
  }

  if (ride.driverId !== userId) {
    return res.status(403).json({ error: "Not authorized to delete this ride" });
  }

  const user = await User.findByPk(userId);
  if (!user) {
    return res.status(404).json({ error: "User not found" });
  }

  const completedBookings = await Booking.findAll({
    where: { 
      rideId: id,
      status: BookingStatus.COMPLETED
    }
  });

  if (completedBookings.length > 0) {
    return res.status(400).json({ 
      error: "Cannot delete ride with completed bookings",
      message: "This ride has completed bookings and cannot be deleted. Completed bookings indicate payouts may have been processed."
    });
  }

  // Get all bookings for this ride
  const bookings = await Booking.findAll({
    where: {
      rideId: ride.id,
      status: { [Op.in]: [BookingStatus.CONFIRMED, BookingStatus.PENDING] }
    },
    include: [{ model: User, as: 'user' }]
  });

  console.log('Number of bookings to process:', bookings.length);

  // Process refunds for confirmed bookings and cancellations for pending bookings
  const refundPromises = bookings.map(async (booking) => {
    if (booking.status === BookingStatus.CONFIRMED) {
      // Handle refund for confirmed bookings
      if (STRIPE_ENABLED && booking.paymentIntentId && booking.paymentIntentId !== 'NO_STRIPE') {
        try {
          // Create a refund for the captured payment
          const refund = await stripe.refunds.create({
            payment_intent: booking.paymentIntentId,
            reason: 'requested_by_customer', // Driver cancellation counts as customer request
            metadata: {
              bookingId: booking.id,
              rideId: ride.id,
              reason: 'driver_cancelled_ride'
            }
          });

          console.log(`Refund created for booking ${booking.id}: ${refund.id}`);
          return { success: true, bookingId: booking.id, refundId: refund.id };
        } catch (stripeError) {
          console.error(`Failed to create refund for booking ${booking.id}:`, stripeError);
          return { success: false, bookingId: booking.id, error: stripeError };
        }
      }
    } else if (booking.status === BookingStatus.PENDING) {
      // Handle cancellation for pending bookings
      if (STRIPE_ENABLED && booking.paymentIntentId && booking.paymentIntentId !== 'NO_STRIPE') {
        try {
          // Check PaymentIntent status first
          const paymentIntent = await stripe.paymentIntents.retrieve(booking.paymentIntentId);

          if (['requires_payment_method', 'requires_capture', 'requires_confirmation', 'requires_action', 'processing'].includes(paymentIntent.status)) {
            await stripe.paymentIntents.cancel(booking.paymentIntentId);
            console.log(`PaymentIntent cancelled for pending booking ${booking.id}`);
          }

          return { success: true, bookingId: booking.id, cancelled: true };
        } catch (stripeError) {
          console.error(`Failed to cancel PaymentIntent for booking ${booking.id}:`, stripeError);
          return { success: false, bookingId: booking.id, error: stripeError };
        }
      }
    }

    return { success: true, bookingId: booking.id, noStripeAction: true };
  });

  // Wait for all refunds/cancellations to complete
  const refundResults = await Promise.allSettled(refundPromises);

  // SendGrid email notification (SEND EMAILS BEFORE UPDATING BOOKING STATUS)
  if (!process.env.SENDGRID_DRIVER_CANCEL_TEMPLATE_ID) {
    throw new Error('SENDGRID_DRIVER_CANCEL_TEMPLATE_ID is not set in environment variables');
  }

  for (const booking of bookings) {
    const passenger = (booking as any).user;
    if (passenger && passenger.email) {
      try {
        // Determine refund message based on ORIGINAL booking status (CONFIRMED OR PENDING)
        let refundMessage = '';
        if (booking.status === BookingStatus.CONFIRMED) {
          refundMessage = 'Your payment has been refunded and should appear in your account within 5-10 business days.';
        } else {
          refundMessage = 'Your ride request was pending, so payment authorization has been canceled. No charges will be made.';
        }

        const estTimezone = 'America/New_York';
        const rideDateTimeUTC = new Date(`${ride.date}T${ride.time || '00:00'}:00Z`);
        const estDate = toZonedTime(rideDateTimeUTC, estTimezone);
        const formattedDate = format(estDate, 'EEEE, MMM d, yyyy');

        await sendDynamicEmail({
          to: passenger.email,
          templateId: process.env.SENDGRID_DRIVER_CANCEL_TEMPLATE_ID!,
          dynamicTemplateData: {
            driverName: user.name,
            passengerName: passenger.name,
            from: ride.from,
            to: ride.to,
            date: formattedDate,
            refundMessage: refundMessage
          },
        });
        console.log(`Email sent to ${passenger.email} with refund message: ${refundMessage}`);
      } catch (err) {
        console.error(`Failed to send email to ${passenger.email}:`, err);
      }
    }
  }

  // NOW Cancel all bookings for this ride (AFTER processing refunds and sending emails)
  await Booking.update(
    { status: BookingStatus.CANCELLED },
    { where: { rideId: ride.id } }
  );

  // Delete ride
  await ride.destroy();

  // Log refund results
  const successfulRefunds = refundResults.filter(result => result.status === 'fulfilled').length;
  const failedRefunds = refundResults.filter(result => result.status === 'rejected').length;

  console.log(`Ride deletion completed. Successful refunds/cancellations: ${successfulRefunds}, Failed: ${failedRefunds}`);

  res.status(200).json({
    message: "Ride deleted and all bookings cancelled. Refunds have been processed for confirmed bookings.",
    refundResults: {
      successful: successfulRefunds,
      failed: failedRefunds
    }
  });
});

// @desc    Get user's rides
// @route   GET /api/rides/user
// @access  Private
// ...existing code...
export const getUserRides = asyncHandler(
  async (req: Request, res: Response) => {
    const userId = req.params.id; 

    if (!userId) {
      throw new AppError("User not found", 404);
    }

    // Get user's rides first (without associations)
    const rides = await Ride.findAll({
      where: {
        driverId: userId,
        [Op.and]: {
          date: {
            [Op.gte]: new Date(),
          },
        },
      },
      order: [
        ["date", "ASC"],
        ["time", "ASC"],
      ],
    });

    // Get completed bookings for all these rides
    const rideIds = rides.map(ride => ride.id);
    const completedBookingsMap = new Map();

    if (rideIds.length > 0) {
      const completedBookings = await Booking.findAll({
        where: {
          rideId: { [Op.in]: rideIds },
          status: BookingStatus.COMPLETED
        }
      });

      completedBookings.forEach(booking => {
        completedBookingsMap.set(booking.rideId, true);
      });
    }

    // Format rides with booking status
    const formattedRides = rides.map((ride) => {
      const hasCompletedBookings = completedBookingsMap.has(ride.id);
      
      return {
        ...ride.toJSON(),
        hasCompletedBookings,
        canDelete: !hasCompletedBookings
      };
    });

    console.log('Formatted rides sample:', formattedRides[0]);
    res.status(200).json(formattedRides);
  }
);

// @desc    Get past rides
// @route   GET /api/rides/past
// @access  Public
export const getPastRides = asyncHandler(
  async (req: Request, res: Response) => {
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // Get rides with date before today
    const rides = await Ride.findAll({
      where: {
        date: {
          [Op.lt]: today,
        },
      },
      include: [
        {
          model: User,
          as: "driver",
          attributes: ["id", "name", "avatar"],
        },
      ],
      order: [
        ["date", "DESC"], // Most recent past rides first
        ["time", "DESC"],
      ],
    });

    // Format rides for frontend
    const formattedRides = rides.map((ride) => {
      const driver = ride.get("driver") as User;

      return {
        id: ride.id,
        from: ride.from,
        to: ride.to,
        date: ride.date,
        time: ride.time,
        seatsAvailable: ride.seatsAvailable,
        totalSeats: ride.totalSeats,
        suitcasesAvailable: ride.suitcasesAvailable,
        totalSuitcases: ride.totalSuitcases,
        price: ride.price,
        postedTime: ride.postedTime,
        notes: ride.notes,
        pickupAddress: ride.pickupAddress,
        pickupLat: ride.pickupLat,
        pickupLng: ride.pickupLng,
        dropoffAddress: ride.dropoffAddress,
        dropoffLat: ride.dropoffLat,
        dropoffLng: ride.dropoffLng,
        driver: {
          id: driver.id,
          name: driver.name,
          info: req.region === 'nj' ? "NJ driver" : "Cornell Verified driver",
          avatarColor: getAvatarColor(driver.id),
          avatar: driver.avatar,
        },
      };
    });

    res.status(200).json(formattedRides);
  }
);

// Helper function to generate consistent avatar colors
function getAvatarColor(id: string): string {
  const colors = [
    "bg-desert-primary",
    "bg-desert-secondary",
    "bg-desert-accent",
  ];
  const index = id.charCodeAt(0) % colors.length;
  return colors[index];
}
